import React, { useState } from 'react'
import { Modal } from '@instructure/ui-modal'
import { Heading } from '@instructure/ui-heading'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'
import { Button } from '@instructure/ui-buttons'
import { Flex } from '@instructure/ui-flex'
import { Badge } from '@instructure/ui-badge'
import { Spinner } from '@instructure/ui-spinner'
import { IconUserLine, IconCalendarMonthLine, IconClockLine } from '@instructure/ui-icons'
import type { ConsultationRequest, FacultyTimeSlot } from '../types'

interface ConsultationRequestDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  consultationRequests: ConsultationRequest[]
  timeSlot: FacultyTimeSlot | null
  datetime: string
  loading: boolean
  onReschedule?: (requestId: string) => void
}

const ConsultationRequestDetailsModal: React.FC<ConsultationRequestDetailsModalProps> = ({
  isOpen,
  onClose,
  consultationRequests,
  timeSlot,
  datetime,
  loading,
  onReschedule
}) => {
  const [selectedRequest, setSelectedRequest] = useState<ConsultationRequest | null>(null)

  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return dateTimeString
    }
  }

  const getStatusBadgeProps = (status: string) => {
    switch (status) {
      case 'pending':
        return { type: 'notification' as const, text: 'Pending' }
      case 'declined':
        return { type: 'danger' as const, text: 'Declined' }
      case 'approved':
        return { type: 'success' as const, text: 'Approved' }
      default:
        return { type: 'secondary' as const, text: status }
    }
  }

  const renderRequestCard = (request: ConsultationRequest) => {
    const badgeProps = getStatusBadgeProps(request.status)
    
    return (
      <View
        key={request.id}
        as="div"
        background="secondary"
        padding="medium"
        borderRadius="medium"
        borderWidth="small"
        borderColor="brand"
        margin="0 0 medium 0"
      >
        <Flex direction="column" gap="small">
          <Flex justifyItems="space-between" alignItems="start">
            <View as="div">
              <Flex alignItems="center" gap="x-small" margin="0 0 x-small 0">
                <IconUserLine size="x-small" />
                <Text weight="bold" size="medium">
                  {request.student_name}
                </Text>
                <Badge {...badgeProps} />
              </Flex>
              <Text size="small" color="secondary">
                Student ID: {request.student_id}
              </Text>
              {request.student_department && (
                <Text size="small" color="secondary" display="block">
                  Department: {request.student_department}
                </Text>
              )}
            </View>
          </Flex>

          <View as="div">
            <Flex alignItems="center" gap="x-small" margin="0 0 x-small 0">
              <IconCalendarMonthLine size="x-small" />
              <Text weight="bold" size="small">Requested Time:</Text>
            </Flex>
            <Text size="small">{formatDateTime(request.preferred_datetime)}</Text>
          </View>

          <View as="div">
            <Text weight="bold" size="small" display="block" margin="0 0 x-small 0">
              Nature of Concern:
            </Text>
            <Text size="small">{request.concern_type_display}</Text>
          </View>

          <View as="div">
            <Text weight="bold" size="small" display="block" margin="0 0 x-small 0">
              Description:
            </Text>
            <Text size="small">{request.description}</Text>
          </View>

          {request.faculty_comment && (
            <View as="div">
              <Text weight="bold" size="small" display="block" margin="0 0 x-small 0">
                Faculty Comment:
              </Text>
              <Text size="small">{request.faculty_comment}</Text>
            </View>
          )}

          <View as="div">
            <Text size="x-small" color="secondary">
              Submitted: {new Date(request.created_at).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: 'numeric',
                minute: '2-digit'
              })}
            </Text>
          </View>

          {request.status === 'pending' && onReschedule && (
            <View as="div" margin="small 0 0 0">
              <Button
                size="small"
                onClick={() => onReschedule(request.id)}
              >
                Reschedule
              </Button>
            </View>
          )}
        </Flex>
      </View>
    )
  }

  const modalTitle = consultationRequests.length > 1
    ? `Consultation Requests (${consultationRequests.length})`
    : 'Consultation Request Details'

  // Don't render if timeSlot is null
  if (!timeSlot) {
    return null
  }

  return (
    <Modal
      open={isOpen}
      onDismiss={onClose}
      size="medium"
      label={modalTitle}
      className="consultation-modal"
    >
      <Modal.Header>
        <Heading level="h2">
          {modalTitle}
        </Heading>
      </Modal.Header>

      <Modal.Body>
        {loading ? (
          <View as="div" className="modal-loading">
            <Spinner renderTitle="Loading consultation requests..." />
            <Text className="loading-text">Loading consultation requests...</Text>
          </View>
        ) : (
          <View as="div">
            <View as="div" className="time-slot-info">
              <div className="slot-title">
                <IconClockLine size="small" />
                <Text weight="bold">Time Slot</Text>
              </div>
              <div className="slot-details">
                {timeSlot.is_recurring
                  ? `${timeSlot.day_of_week} ${timeSlot.start_time} - ${timeSlot.end_time}`
                  : `${timeSlot.specific_date} ${timeSlot.start_time} - ${timeSlot.end_time}`
                }
              </div>
            </View>

            {consultationRequests.length === 0 ? (
              <View as="div" className="empty-requests">
                <div className="empty-icon">📋</div>
                <div className="empty-message">No consultation requests found for this time slot.</div>
              </View>
            ) : (
              <View as="div">
                {consultationRequests.map(renderRequestCard)}
              </View>
            )}
          </View>
        )}
      </Modal.Body>

      <Modal.Footer>
        <Button onClick={onClose}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default ConsultationRequestDetailsModal
