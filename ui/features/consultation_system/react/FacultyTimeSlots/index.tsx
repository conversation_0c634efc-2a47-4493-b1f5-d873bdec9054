import React, { useState, useEffect } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Alert } from '@instructure/ui-alerts'
import { Spinner } from '@instructure/ui-spinner'
import { IconClockLine } from '@instructure/ui-icons'
import TimeSlotList from './TimeSlotList'
import TimeSlotCalendar from './TimeSlotCalendar'
import TimeSlotModal from './TimeSlotModal'
import ConsultationRequestDetailsModal from './ConsultationRequestDetailsModal'
import { fetchTimeSlots, createTimeSlot, updateTimeSlot, deleteTimeSlot, fetchTimeSlotConsultationRequests } from '../services/facultyTimeSlotsApi'
import type { FacultyTimeSlot, TimeSlotFormData, ConsultationRequest } from '../types'

interface FacultyTimeSlotsProps {
  currentUserId: string
  initialTimeSlots?: FacultyTimeSlot[]
  daysOfWeek: string[]
}

const FacultyTimeSlots: React.FC<FacultyTimeSlotsProps> = ({
  currentUserId,
  initialTimeSlots = [],
  daysOfWeek
}) => {
  const [timeSlots, setTimeSlots] = useState<FacultyTimeSlot[]>(initialTimeSlots)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showTimeSlotModal, setShowTimeSlotModal] = useState(false)
  const [showConsultationModal, setShowConsultationModal] = useState(false)
  const [editingSlot, setEditingSlot] = useState<FacultyTimeSlot | null>(null)
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar')
  const [prefilledFormData, setPrefilledFormData] = useState<Partial<TimeSlotFormData> | null>(null)
  const [calendarRefresh, setCalendarRefresh] = useState<(() => void) | null>(null)
  const [consultationRequests, setConsultationRequests] = useState<ConsultationRequest[]>([])
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<FacultyTimeSlot | null>(null)
  const [selectedDateTime, setSelectedDateTime] = useState<string>('')
  const [consultationLoading, setConsultationLoading] = useState(false)

  useEffect(() => {
    if (initialTimeSlots.length === 0) {
      loadTimeSlots()
    }
  }, [])

  const loadTimeSlots = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetchTimeSlots()
      setTimeSlots(response.time_slots)
    } catch (err) {
      setError('Failed to load time slots. Please try again.')
      console.error('Error loading time slots:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateSlot = async (formData: TimeSlotFormData) => {
    try {
      setLoading(true)
      setError(null)
      const newSlot = await createTimeSlot(formData)
      setTimeSlots(prev => [...prev, newSlot])
      setShowTimeSlotModal(false)
      setSuccess('Time slot created successfully!')
      setTimeout(() => setSuccess(null), 5000)

      // Refresh calendar if in calendar view
      if (viewMode === 'calendar' && calendarRefresh) {
        calendarRefresh()
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create time slot. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateSlot = async (id: string, formData: TimeSlotFormData) => {
    try {
      setLoading(true)
      setError(null)
      const updatedSlot = await updateTimeSlot(id, formData)
      setTimeSlots(prev => prev.map(slot => slot.id === id ? updatedSlot : slot))
      setEditingSlot(null)
      setSuccess('Time slot updated successfully!')
      setTimeout(() => setSuccess(null), 5000)

      // Refresh calendar if in calendar view
      if (viewMode === 'calendar' && calendarRefresh) {
        calendarRefresh()
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update time slot. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteSlot = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await deleteTimeSlot(id)
      setTimeSlots(prev => prev.filter(slot => slot.id !== id))
      setSuccess('Time slot deleted successfully!')
      setTimeout(() => setSuccess(null), 5000)

      // Refresh calendar if in calendar view
      if (viewMode === 'calendar' && calendarRefresh) {
        calendarRefresh()
      }
    } catch (err: any) {
      setError(err.message || 'Failed to delete time slot. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleEditSlot = (slot: FacultyTimeSlot) => {
    setEditingSlot(slot)
    setPrefilledFormData(null)
    setShowTimeSlotModal(true)
  }

  const handleCancelTimeSlotModal = () => {
    setShowTimeSlotModal(false)
    setEditingSlot(null)
    setPrefilledFormData(null)
  }

  const handleAddTimeSlotFromCalendar = (startTime: string, endTime: string, dayOfWeek: string, specificDate?: string) => {
    // Prepare prefilled form data
    const formData: Partial<TimeSlotFormData> = {
      start_time: startTime,
      end_time: endTime,
      day_of_week: dayOfWeek,
      is_recurring: false, // Default to specific date when clicking from calendar
      specific_date: specificDate || '',
      is_available: true, // Default to available
      notes: ''
    }

    setPrefilledFormData(formData)
    setEditingSlot(null) // Make sure we're not in edit mode
    setShowTimeSlotModal(true) // Show the modal
    clearMessages()
  }

  const clearMessages = () => {
    setError(null)
    setSuccess(null)
  }

  const handleCalendarRefreshReady = (refreshFn: () => void) => {
    setCalendarRefresh(() => refreshFn)
  }

  const handleViewConsultationRequests = async (slot: any) => {
    try {
      setConsultationLoading(true)
      setSelectedTimeSlot(slot.slot_data)
      setSelectedDateTime(slot.datetime)

      const result = await fetchTimeSlotConsultationRequests(slot.faculty_time_slot_id, slot.datetime)

      if (result.hasError) {
        setError(result.errorMessage || 'Failed to fetch consultation requests')
        return
      }

      if (result.data && slot.slot_data) {
        setConsultationRequests(result.data.consultation_requests)
        setShowConsultationModal(true)
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch consultation requests')
    } finally {
      setConsultationLoading(false)
    }
  }

  const handleCloseConsultationModal = () => {
    setShowConsultationModal(false)
    setConsultationRequests([])
    setSelectedTimeSlot(null)
    setSelectedDateTime('')
  }

  return (
    <div className="consultation-system">
      <View as="div" padding="large">
        <div className="page-header">
          <Heading level="h1" margin="0 0 small 0">
            <IconClockLine /> Manage Consultation Time Slots
          </Heading>
          <p>Define your available time slots for student consultations. Students will be able to request appointments during these times.</p>
        </div>

        {error && (
          <Alert variant="error" margin="0 0 medium 0" onDismiss={clearMessages}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert variant="success" margin="0 0 medium 0" onDismiss={clearMessages}>
            {success}
          </Alert>
        )}

        <View as="div" margin="0 0 large 0">
          <View as="div" display="flex">
            <View as="div" margin="0 small 0 0">
              <Button
                color="primary"
                onClick={() => {
                  setEditingSlot(null)
                  setPrefilledFormData(null)
                  setShowTimeSlotModal(true)
                }}
                disabled={loading}
              >
                Add New Time Slot
              </Button>
            </View>

            <View as="div" display="flex">
              <Button
                color={viewMode === 'calendar' ? 'primary' : 'secondary'}
                onClick={() => setViewMode('calendar')}
                size="small"
                margin="0 small 0 0"
              >
                Calendar View
              </Button>
              <Button
                color={viewMode === 'list' ? 'primary' : 'secondary'}
                onClick={() => setViewMode('list')}
                size="small"
              >
                List View
              </Button>
            </View>
          </View>
        </View>

        {loading ? (
          <View as="div" textAlign="center" padding="large">
            <Spinner renderTitle="Loading time slots..." />
          </View>
        ) : (
          <>
            {viewMode === 'calendar' ? (
              <TimeSlotCalendar
                currentUserId={currentUserId}
                onEdit={handleEditSlot}
                onDelete={handleDeleteSlot}
                onAddTimeSlot={handleAddTimeSlotFromCalendar}
                onViewConsultationRequests={handleViewConsultationRequests}
                loading={loading}
                onRefreshReady={handleCalendarRefreshReady}
              />
            ) : (
              <TimeSlotList
                timeSlots={timeSlots}
                onEdit={handleEditSlot}
                onDelete={handleDeleteSlot}
                loading={loading}
              />
            )}
          </>
        )}

        {!loading && timeSlots.length === 0 && !showTimeSlotModal && (
          <View as="div" textAlign="center" padding="x-large">
            <div className="empty-state">
              <div className="empty-icon">
                <IconClockLine size="large" />
              </div>
              <Heading level="h3" margin="0 0 small 0">
                No Time Slots Defined
              </Heading>
              <p>You haven't created any consultation time slots yet. Add your first time slot to start accepting consultation requests from students.</p>
              <Button
                color="primary"
                onClick={() => {
                  setEditingSlot(null)
                  setPrefilledFormData(null)
                  setShowTimeSlotModal(true)
                }}
              >
                Create Your First Time Slot
              </Button>
            </div>
          </View>
        )}

        {/* Time Slot Modal */}
        <TimeSlotModal
          isOpen={showTimeSlotModal}
          onClose={handleCancelTimeSlotModal}
          daysOfWeek={daysOfWeek}
          initialData={editingSlot}
          prefilledData={prefilledFormData}
          onSubmit={editingSlot ?
            (data: TimeSlotFormData) => handleUpdateSlot(editingSlot.id, data) :
            handleCreateSlot
          }
          loading={loading}
        />

        {/* Consultation Request Details Modal */}
        <ConsultationRequestDetailsModal
          isOpen={showConsultationModal}
          onClose={handleCloseConsultationModal}
          consultationRequests={consultationRequests}
          timeSlot={selectedTimeSlot}
          datetime={selectedDateTime}
          loading={consultationLoading}
        />
      </View>
    </div>
  )
}

export default FacultyTimeSlots
